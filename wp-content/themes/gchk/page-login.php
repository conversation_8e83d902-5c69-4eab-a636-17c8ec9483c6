<?php
/**
 * Template Name: User Login
 */

// Handle login form submission
if ($_POST && isset($_POST['login_key']) && isset($_GET['user_id'])) {
    $user_id = intval($_GET['user_id']);
    $login_key = sanitize_text_field($_POST['login_key']);
    
    global $wpdb;
    $table_name = $wpdb->prefix . 'user_auth_keys';
    
    // Verify login key
    $auth_record = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM $table_name 
        WHERE user_id = %d AND login_key = %s AND is_active = 1 AND expires_at > NOW()
    ", $user_id, $login_key));
    
    if ($auth_record) {
        // Update last access time
        $wpdb->update(
            $table_name,
            array('last_access' => current_time('mysql')),
            array('user_id' => $user_id),
            array('%s'),
            array('%d')
        );
        
        // Set session
        session_start();
        $_SESSION['authenticated_user_id'] = $user_id;
        $_SESSION['login_time'] = time();
        
        // Redirect to submissions page
        wp_redirect(home_url('/view-submissions?user_id=' . $user_id));
        exit;
    } else {
        $error_message = "Invalid login key or expired session. Please check your email for the correct key.";
    }
}

get_header(); ?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}

.login-container {
    max-width: 400px;
    margin: 50px auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.login-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.login-header h1 {
    margin: 0;
    font-size: 24px;
}

.login-form {
    padding: 40px 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.login-button {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
}

.info-message {
    background: #d1ecf1;
    color: #0c5460;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #bee5eb;
    font-size: 14px;
}

.security-notice {
    background: #fff3cd;
    color: #856404;
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    border: 1px solid #ffeaa7;
    font-size: 14px;
}
</style>

<div class="login-container">
    <div class="login-header">
        <h1>🔐 Secure Access</h1>
        <p>Enter your login key to access your submissions</p>
    </div>
    
    <div class="login-form">
        <?php if (isset($error_message)): ?>
            <div class="error-message">
                <?php echo esc_html($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_GET['user_id'])): ?>
            <div class="info-message">
                Please enter the login key that was sent to your email address.
            </div>
            
            <form method="post" action="">
                <div class="form-group">
                    <label for="login_key">Login Key:</label>
                    <input type="password" id="login_key" name="login_key" required 
                           placeholder="Enter your 16-character login key" maxlength="16">
                </div>
                
                <button type="submit" class="login-button">Access My Submissions</button>
            </form>
            
            <div class="security-notice">
                <strong>Security Notice:</strong> Your session will automatically expire after 15 minutes of inactivity for your protection.
            </div>
        <?php else: ?>
            <div class="error-message">
                Invalid access. Please use the link provided in your email.
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Disable right-click context menu
document.addEventListener('contextmenu', function(e) {
    e.preventDefault();
});

// Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
document.addEventListener('keydown', function(e) {
    if (e.keyCode == 123 || // F12
        (e.ctrlKey && e.shiftKey && e.keyCode == 73) || // Ctrl+Shift+I
        (e.ctrlKey && e.shiftKey && e.keyCode == 74) || // Ctrl+Shift+J
        (e.ctrlKey && e.keyCode == 85)) { // Ctrl+U
        e.preventDefault();
        return false;
    }
});

// Disable text selection
document.addEventListener('selectstart', function(e) {
    e.preventDefault();
});

// Disable drag
document.addEventListener('dragstart', function(e) {
    e.preventDefault();
});
</script>

<?php get_footer(); ?>
